@import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css);

footer {
  position: relative;
  background-color: #051018;
  color: #fff;
  padding: 20px;
  text-align: center;
}
.container {
  display: flex;
}
.child-cont {
  flex: 1;
}
.license {
  text-align: end;
  margin-bottom: 1px;
  margin-right: 1rem;
}
.final {
  color: #646464;
  text-align: end;
}
.text {
  padding: 10px;
  text-align: end;
  font-style: normal;
  color: #fffefe;
  font-family: sans-serif;
}
.support-logo {
  margin-top: 1rem;
  height: 50px;
}
.contact {
  text-align: start;
}

/* Footer icons */
.footer-icons {
  margin-left: 1rem;
  display: flex;
  justify-content: start;
  align-items: center;
}

.footer-icon {
  color: #fff;
  font-size: 24px;
  margin: 0 10px;
  text-decoration: none;
}

/* Style the icons on hover */
.footer-icon:hover {
  opacity: 0.7;
  position: relative;
  display: inline-block;
}
.message {
  display: none;
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px;
}
.social-icons li a:hover {
  color: #000;
  display: block;
}
@media screen and (max-width: 800px) {
  .child-cont {
    display: none;
  }
  .container {
    margin-bottom: 2rem;
  }
}
