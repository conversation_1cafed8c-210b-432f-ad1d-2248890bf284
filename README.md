# Moodie
A **MERN Stack** web application for analyzing sentiment in text.

<img src="https://github.com/puneeth072003/Moodie/assets/119479391/0642746b-9154-41f8-9a7d-e752b007b645" alt="Logo" style="float:right;">

This project is a sentiment analysis application built using the MERN Stack:
- Frontend: React.js
- Backend: Node.js with Express.js
- Sentiment Analysis Engine: VADER

## Working
- A React-Node.js project created for evaluating an individual’s digital,social
well-being through sentiment analysis using VADER.
- The project aims to analyze Reddit posts, assessing negativity, positivity,
neutrality, and estimating overall mental health of the individual.

## Getting Started
- Clone the repository.
- Start frontend app: 
  ```
  cd Moodie && npm run dev
  ```
- Start the backend server:
  ```
  npm install
  ```
- Visit `http://localhost:5173` in your browser.
