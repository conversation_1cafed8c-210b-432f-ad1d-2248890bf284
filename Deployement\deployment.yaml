apiVersion: apps/v1
kind: Deployment
metadata:
  name: moodie-backend-deployment
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: moodie-backend
  template:
    metadata:
      labels:
        app: moodie-backend
    spec:
      containers:
      - name: moodie-backend-container
        image: puneeth773/moodie:v1.0.3
        ports:
        - containerPort: 5000
        env:
        - name: VIRTUAL_ENV
          value: "/opt/venv"
        - name: PATH
          value: "/opt/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /metrics
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /metrics
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: moodie-backend-service
  namespace: default
spec:
  selector:
    app: moodie-backend
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 5000
      nodePort: 31838
  type: NodePort
