.final-container {
  background-color: #081c29;
  padding-bottom: 6rem;
  padding-top: 15rem;
}
.final-textbox {
  padding-left: 5rem;
}
.final-head {
  font-size: 4rem;
  color: #04a2e2;
}
.final-body {
  font-size: 1.3rem;
  color: #fff;
}
.progress-container {
  height: 5rem;
  margin: 5rem;
  margin-top: 0;
  margin-bottom: 0;
}
.final-submit {
  background-color: #04a2e2;
  font-size: 1rem;
  border: none;
  border-radius: 3px;
  margin-top: 1rem;
  padding: 0.5rem;
  font-family: spotify-circular;
}
.final-input {
  margin-top: 1rem;
  font-size: 1rem;
  background-color: #04a2e2; /* Match button background color */
  border: none; /* Remove default border */
  border-radius: 3px; /* Match button border radius */ /* Match button margin */
  padding: 0.5rem; /* Match button padding */
  font-family: spotify-circular; /* Match button font family */
  color: #fff; /* Text color */
  width: auto; /* Adjust width as needed */
  display: block; /* Display as block for full width */
}
.final-suggest {
  text-align: end;
  font-size: 3rem;
  padding-right: 5rem;
  color: #04a2e2;
}
.final-stext {
  color: #fff;
  text-align: end;
  font-size: 1.7rem;
  padding-right: 5rem;
}
.final-overall {
  color: #04a2e2;
  padding-top: 1rem;
  padding-left: 5rem;
}
.final-flex{
  display:flex;
  padding:none;
}
.final-logo{
  padding-left: 6rem;
  padding-right: 9rem;
  padding-top:1rem;
}
.final-flex-suggest{
  display:flex;
  padding:none;
  justify-content:  space-between;
}
.positive-mood {
  color: #4caf50;
}

.neutral-mood {
  color:#2196f3;
}

.negative-mood {
  color: #f44336;
}